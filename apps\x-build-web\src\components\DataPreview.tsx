import { DownloadOutlined } from "@ant-design/icons";
import { Button, Pagination, Table, TableProps, Typography } from "antd";
import { useState } from "react";
import { StructureItem, DataItem } from "../types";

const structureData: StructureItem[] = [
	{
		key: '1',
		id: 1,
		type: '整数',
		name: '字段名',
		description: '字段说明'
	},
	{
		key: '2',
		id: 2,
		type: '文本',
		name: '字段名',
		description: '字段说明'
	},
	{
		key: '3',
		id: 3,
		type: '日期',
		name: '字段名',
		description: '字段说明'
	},
]
const dataSource: DataItem[] = [
	{
		key: '1',
		majorName: '分析对象',
		majorDirection: '',
		analyst: '分析师1',
		time: '2025-03-01',
		platform: '抖音',
		voiceVolume: 1,
		interactionRate: 5,
		commentCount: 1,
		forwardCount: 1,
		likeCount: 1,
		viewCount: 1
	},
	{
		key: '2',
		majorName: '对象',
		majorDirection: '',
		analyst: '分析师1',
		time: '2025-02-28',
		platform: '微信',
		voiceVolume: 2,
		interactionRate: 10,
		commentCount: 2,
		forwardCount: 2,
		likeCount: 2,
		viewCount: 2
	},
	{
		key: '3',
		majorName: '平台',
		majorDirection: '',
		analyst: '分析师1',
		time: '2025-02-27',
		platform: '微博',
		voiceVolume: 3,
		interactionRate: 15,
		commentCount: 3,
		forwardCount: 3,
		likeCount: 3,
		viewCount: 3
	},
	{
		key: '4',
		majorName: '声量数据',
		majorDirection: '',
		analyst: '分析师1',
		time: '2025-02-26',
		platform: '小红书',
		voiceVolume: 3,
		interactionRate: 15,
		commentCount: 3,
		forwardCount: 3,
		likeCount: 3,
		viewCount: 3
	},
	{
		key: '5',
		majorName: '互动量数据',
		majorDirection: '',
		analyst: '分析师1',
		time: '2025-02-25',
		platform: 'bilibili',
		voiceVolume: 3,
		interactionRate: 15,
		commentCount: 3,
		forwardCount: 3,
		likeCount: 3,
		viewCount: 3
	},
	{
		key: '6',
		majorName: '点赞数',
		majorDirection: '',
		analyst: '分析师1',
		time: '2025-02-24',
		platform: '知乎',
		voiceVolume: 1,
		interactionRate: 5,
		commentCount: 1,
		forwardCount: 1,
		likeCount: 1,
		viewCount: 1
	},
	{
		key: '7',
		majorName: '评论数',
		majorDirection: '',
		analyst: '分析师1',
		time: '2025-02-23',
		platform: '知乎',
		voiceVolume: 1,
		interactionRate: 5,
		commentCount: 1,
		forwardCount: 1,
		likeCount: 1,
		viewCount: 1
	},
	{
		key: '8',
		majorName: '转发数',
		majorDirection: '',
		analyst: '分析师1',
		time: '2025-02-22',
		platform: '知乎',
		voiceVolume: 1,
		interactionRate: 5,
		commentCount: 1,
		forwardCount: 1,
		likeCount: 1,
		viewCount: 1
	},
	{
		key: '9',
		majorName: '浏览数',
		majorDirection: '',
		analyst: '分析师1',
		time: '2025-02-21',
		platform: '知乎',
		voiceVolume: 1,
		interactionRate: 5,
		commentCount: 1,
		forwardCount: 1,
		likeCount: 1,
		viewCount: 1
	},
	{
		key: '10',
		majorName: '收藏数',
		majorDirection: '',
		analyst: '分析师1',
		time: '2025-02-20',
		platform: '知乎',
		voiceVolume: 1,
		interactionRate: 5,
		commentCount: 1,
		forwardCount: 1,
		likeCount: 1,
		viewCount: 1
	}
];
const { Title } = Typography;

const DataPreview = () => {
	const [currentPage, setCurrentPage] = useState(1);
	const [pageSize, setPageSize] = useState(10);
	const structureColumns: TableProps<StructureItem>['columns'] = [
		{
			title: '序号',
			dataIndex: 'id',
			key: 'id',
			width: 60,
		},
		{
			title: '字段类型',
			dataIndex: 'type',
			key: 'type',
			width: 100,
		},
		{
			title: '字段名',
			dataIndex: 'name',
			key: 'name',
			width: 100,
		},
		{
			title: '字段说明',
			dataIndex: 'description',
			key: 'description',
			width: 100,
		},
	]
	const dataColumns: TableProps<DataItem>['columns'] = [
		{
			title: '专业名称',
			dataIndex: 'majorName',
			key: 'majorName',
			width: 120,
		},
		{
			title: '专业方向',
			dataIndex: 'majorDirection',
			key: 'majorDirection',
			width: 120,
		},
		{
			title: '分析对象',
			dataIndex: 'analyst',
			key: 'analyst',
			width: 120,
		},
		{
			title: '时间',
			dataIndex: 'time',
			key: 'time',
			width: 120,
		},
		{
			title: '平台',
			dataIndex: 'platform',
			key: 'platform',
			width: 100,
		},
		{
			title: '声量数据',
			dataIndex: 'voiceVolume',
			key: 'voiceVolume',
			width: 100,
		},
		{
			title: '互动量数据',
			dataIndex: 'interactionRate',
			key: 'interactionRate',
			width: 100,
		},
		{
			title: '评论数',
			dataIndex: 'commentCount',
			key: 'commentCount',
			width: 80,
		},
		{
			title: '转发数',
			dataIndex: 'forwardCount',
			key: 'forwardCount',
			width: 80,
		},
		{
			title: '点赞数',
			dataIndex: 'likeCount',
			key: 'likeCount',
			width: 80,
		},
		{
			title: '浏览数',
			dataIndex: 'viewCount',
			key: 'viewCount',
			width: 80,
		},
	];
	const handlePageChange = (page: number, pageSize?: number) => {
		setCurrentPage(page);
		if (pageSize) setPageSize(pageSize);
	};
	return (
		<div className="bg-white shadow-sm flex flex-1">
			{/* 左侧区域 - Tab 和表结构 */}
			<div className="w-1/3 pr-2">
				{/* <div className="mb-4"> */}
				<div className="flex justify-between items-center mb-2">
					<Title level={5} className="m-0">表结构</Title>
					<Button type="primary" size="small">编辑</Button>
				</div>
				<Table
					columns={structureColumns}
					dataSource={structureData}
					pagination={false}
					bordered
					className="mb-2"
				/>
				{/* </div> */}
			</div>
			{/* 中间分割线 */}
			<div className="border-l border-gray-200" />
			{/* 右侧区域 - 数据表格 */}
			<div className="w-2/3 pl-4">
				<div className="flex justify-end items-center mb-2">
					{/* <Tooltip title="显示所有数据的前100条数据">
						<Text className="mr-2">显示所有数据的前100条数据</Text>
					</Tooltip> */}
					<Button type="link" icon={<DownloadOutlined />} />
				</div>
				<Table
					columns={dataColumns}
					dataSource={dataSource}
					pagination={false}
					scroll={{ x: 1000, y: 500 }}
					bordered
					className="mb-4"
				/>
				<div className="flex justify-end mt-2">
					<Pagination
						current={currentPage}
						pageSize={pageSize}
						total={dataSource.length}
						onChange={handlePageChange}
						showSizeChanger
						showQuickJumper
						showTotal={(total) => `共 ${total} 条`}
					/>
				</div>
			</div>
		</div>
	)
}
export default DataPreview;