import { FieldTimeOutlined, FilterOutlined, MergeCellsOutlined, PlusSquareOutlined, SortAscendingOutlined, TableOutlined, DeleteOutlined } from "@ant-design/icons"
import { Button, Timeline, Select, Modal, Checkbox, Form, Input, Radio, Table, Splitter } from "antd"
import { useState, useMemo } from "react"
import FormulaEditor from "./FormulaEditor"
import { useQuery } from '@tanstack/react-query';
import { trpc } from '../service/tRPC';
import FilterOperation from "./FilterOperation";
import PivotOperation from './PivotOperation';
import SortOperation, { SortCondition } from './SortOperation';
import TimeAggregationOperation, { TimeAggregation } from './TimeAggregationOperation';
import TableMergeOperation, { TableMergeConfig } from './TableMergeOperation';
import { nanoid } from "nanoid";

// 过滤条件节点类型（与FilterOperation组件保持一致）
type FilterNode = {
	id: string;
	type: 'condition' | 'group';
	// 条件节点属性
	field?: string;
	operator?: string;
	value?: string;
	valueEnd?: string;
	uniqueValues?: boolean;
	// 组节点属性
	logic?: 'AND' | 'OR';
	children?: FilterNode[];
};

// 操作记录项类型
type OperationRecord = {
	id: string;
	type: 'pivot' | 'merge' | 'timeagg' | 'sort' | 'filter' | 'formula';
	name: string;
	icon: React.ReactNode;
	timestamp: Date;
	isActive?: boolean;
};


// mock表格数据
const mockData = [
	{
		key: "1",
		object: "分析对象1",
		date: "2025-03-01",
		platform: "抖音",
		voice: 1,
		interact: 5,
		like: 1,
		comment: 1,
		repost: 1,
		read: 1,
		collect: 1,
	},
	{
		key: "2",
		object: "分析对象1",
		date: "2025-02-28",
		platform: "微信",
		voice: 2,
		interact: 10,
		like: 2,
		comment: 2,
		repost: 2,
		read: 2,
		collect: 2,
	},
	{
		key: "3",
		object: "分析对象1",
		date: "2025-02-27",
		platform: "微博",
		voice: 3,
		interact: 15,
		like: 3,
		comment: 3,
		repost: 3,
		read: 3,
		collect: 3,
	},
	{
		key: "4",
		object: "分析对象1",
		date: "2025-02-26",
		platform: "小红书",
		voice: 3,
		interact: 15,
		like: 3,
		comment: 3,
		repost: 3,
		read: 3,
		collect: 3,
	},
	{
		key: "5",
		object: "分析对象1",
		date: "2025-02-25",
		platform: "bilibili",
		voice: 3,
		interact: 15,
		like: 3,
		comment: 3,
		repost: 3,
		read: 3,
		collect: 3,
	},
	{
		key: "6",
		object: "分析对象1",
		date: "2025-02-24",
		platform: "知乎",
		voice: 1,
		interact: 5,
		like: 1,
		comment: 1,
		repost: 1,
		read: 1,
		collect: 1,
	},
]

// mock列定义
const allColumns = [
	{ title: "分析对象", dataIndex: "object", key: "object" },
	{ title: "时间", dataIndex: "date", key: "date" },
	{ title: "平台", dataIndex: "platform", key: "platform" },
	{ title: "声音数据", dataIndex: "voice", key: "voice" },
	{ title: "互动数据", dataIndex: "interact", key: "interact" },
	{ title: "点赞数", dataIndex: "like", key: "like" },
	{ title: "评论数", dataIndex: "comment", key: "comment" },
	{ title: "转发数", dataIndex: "repost", key: "repost" },
	{ title: "阅读数", dataIndex: "read", key: "read" },
	{ title: "收藏数", dataIndex: "collect", key: "collect" },
]
// 获取可用的表列表（模拟工作区表数据）
const getAvailableTables = () => {
	// 模拟工作区中的表列表，实际应用中应该从API获取
	return [
		'声量互动量表',
		'互动量表',
		'词云表',
		'原文表',
		'舆情互动量',
		'舆情比表'
	];
};

// 获取指定表的字段列表
const getTableFields = (tableName: string) => {
	// 模拟不同表的字段结构，实际应用中应该从API获取
	const tableFieldsMap: Record<string, string[]> = {
		'声量互动量表': ['date', 'source', 'volume', 'totalCount', 'readCount', 'likeCount'],
		'互动量表': ['date', 'source', 'totalCount', 'readCount', 'likeCount'],
		'词云表': ['word', 'count', 'category'],
		'原文表': ['id', 'content', 'author', 'publishTime'],
		'舆情互动量': ['date', 'platform', 'sentiment', 'count'],
		'舆情比表': ['name', 'province', 'chineseName', 'code']
	};
	return tableFieldsMap[tableName] || [];
};
// 获取模拟表数据
const getMockTableData = (tableName: string) => {
	// 模拟不同表的数据，实际应用中应该从API获取
	const mockTableData: Record<string, any[]> = {
		'声量互动量表': [
			{ date: '2025-04-01', source: 'weibo', volume: 10000, totalCount: 1000, readCount: 800, likeCount: 230 },
			{ date: '2025-04-01', source: 'xiaohongshu', volume: 2000, totalCount: 1200, readCount: 200, likeCount: 1000 },
			{ date: '2025-04-02', source: 'weibo', volume: 8000, totalCount: 800, readCount: 400, likeCount: 230 }
		],
		'互动量表': [
			{ date: '2025-04-01', source: 'weibo', totalCount: 1000, readCount: 800, likeCount: 230 },
			{ date: '2025-04-01', source: 'xiaohongshu', totalCount: 1200, readCount: 200, likeCount: 1000 }
		],
		'舆情比表': [
			{ name: 'Shanghai', province: 'Shanghai', chineseName: '上海', code: '021' },
			{ name: 'Beijing', province: 'Beijing', chineseName: '北京', code: '010' },
			{ name: 'Guangzhou', province: 'Guangdong', chineseName: '广州', code: '020' }
		]
	};

	return mockTableData[tableName] || [];
};

const ViewDetail = () => {
	const fieldKeys = Object.keys(mockData[0] || {});
	const [filters, setFilters] = useState<
		{ field: string; value: string | null }[]
	>([]);
	// 树状过滤器状态
	const [filterNodes, setFilterNodes] = useState<FilterNode[]>([]);
	const [pivotColumns, setPivotColumns] = useState<
		{ field: string }[]
	>([]);

	const [pivotRows, setPivotRows] = useState<
		{ field: string; sort?: 'asc' | 'desc' | null }[]
	>([]);

	const [pivotValues, setPivotValues] = useState<
		{
			field: string;
			agg: 'sum' | 'count' | 'avg' | 'max' | 'min' | 'first' | 'last' | 'median' | 'join';
			joinSeparator?: string;
			sort?: 'asc' | 'desc' | null;
		}[]
	>([]);

	// 排序条件状态
	const [sortConditions, setSortConditions] = useState<SortCondition[]>([]);

	// 时间聚合条件状态
	const [timeAggregations, setTimeAggregations] = useState<TimeAggregation[]>([]);

	// 表合并配置状态
	const [tableMergeConfigs, setTableMergeConfigs] = useState<TableMergeConfig[]>([]);
	// 列设置弹窗显示状态
	const [colModalOpen, setColModalOpen] = useState(false)
	// 当前勾选显示的列
	const [checkedCols, setCheckedCols] = useState(allColumns.map(col => col.key))
	// 新建视图弹窗显示状态
	const [createModalOpen, setCreateModalOpen] = useState(false)
	// 删除弹窗显示状态
	const [deleteModalOpen, setDeleteModalOpen] = useState(false)
	const [formulaOpen, setFormulaOpen] = useState(false)
	// 新建视图表单
	const [form] = Form.useForm()
	const [columns, setColumns] = useState(() => allColumns)
	const [operation, setOperation] = useState('')
	// 处理列勾选变化
	const handleColChange = (checkedValues: string[]) => {
		setCheckedCols(checkedValues)
	}

	// 操作记录状态
	const [operationRecords, setOperationRecords] = useState<OperationRecord[]>([]);
	// 获取操作配置
	const getOperationConfig = (type: string) => {
		const configs = {
			pivot: { name: '数据透视', icon: <TableOutlined /> },
			merge: { name: '表合并', icon: <MergeCellsOutlined /> },
			timeagg: { name: '时间聚合', icon: <FieldTimeOutlined /> },
			sort: { name: '排序', icon: <SortAscendingOutlined /> },
			filter: { name: '筛选', icon: <FilterOutlined /> },
			formula: { name: '新增计算列', icon: <PlusSquareOutlined /> },
		};
		return configs[type as keyof typeof configs] || { name: '未知操作', icon: null };
	};

	// 添加操作记录
	const addOperationRecord = (type: string) => {
		const config = getOperationConfig(type);
		const newRecord: OperationRecord = {
			id: nanoid(),
			type: type as OperationRecord['type'],
			name: config.name,
			icon: config.icon,
			timestamp: new Date(),
			isActive: true,
		};

		setOperationRecords(prev => {
			// 将之前的记录设为非激活状态
			const updatedRecords = prev.map(record => ({ ...record, isActive: false }));
			return [...updatedRecords, newRecord];
		});
	};

	// 切换到指定操作记录
	const switchToOperationRecord = (id: string) => {
		const record = operationRecords.find(r => r.id === id);
		if (record) {
			setOperation(record.type);
			setOperationRecords(prev =>
				prev.map(r => ({ ...r, isActive: r.id === id }))
			);
		}
	};

	// 删除操作记录
	const handleDeleteOperationRecord = (id: string) => {
		Modal.confirm({
			title: '确认删除',
			content: '确定要删除该操作记录吗？',
			okText: '删除',
			okType: 'danger',
			cancelText: '取消',
			onOk: () => {
				setOperationRecords(records => records.filter(record => record.id !== id));
				// 如果删除的是当前激活的操作，清空操作状态
				const deletedRecord = operationRecords.find(record => record.id === id);
				if (deletedRecord?.isActive) {
					setOperation('');
				}
			},
		});
	};

	// 计算所有已选字段（排除当前项自身）
	const getAvailableFields = (currentField: string, currentIdx: number, type: 'filters' | 'columns' | 'rows' | 'values') => {
		const selected = [
			...filters.map((f, i) => (type === 'filters' && i === currentIdx ? null : f.field)),
			...pivotColumns.map((c, i) => (type === 'columns' && i === currentIdx ? null : c.field)),
			...pivotRows.map((r, i) => (type === 'rows' && i === currentIdx ? null : r.field)),
			...pivotValues.map((v, i) => (type === 'values' && i === currentIdx ? null : v.field)),
		].filter(Boolean);
		return fieldKeys.filter(key => !selected.includes(key) || key === currentField);
	};

	// 计算排序功能可用字段（排除当前项自身，允许重复选择）
	const getAvailableFieldsForSort = (currentField: string, currentIdx: number) => {
		// 排序功能允许同一字段被多次选择（不同优先级），所以返回所有字段
		return fieldKeys;
	};

	// 判断字段是否为时间字段
	const isTimeField = (field: string) => {
		// 检查字段名是否包含时间相关关键词
		const timeKeywords = ['date', 'time', 'created', 'updated', 'timestamp'];
		const fieldLower = field.toLowerCase();

		if (timeKeywords.some(keyword => fieldLower.includes(keyword))) {
			return true;
		}

		// 检查字段值是否为日期格式
		if (mockData.length > 0) {
			const sampleValue = mockData[0][field as keyof typeof mockData[0]];
			if (typeof sampleValue === 'string') {
				// 检查是否为日期格式 (YYYY-MM-DD, YYYY/MM/DD, etc.)
				const dateRegex = /^\d{4}[-/]\d{1,2}[-/]\d{1,2}$/;
				return dateRegex.test(sampleValue);
			}
		}

		return false;
	};

	// 应用时间聚合逻辑
	const applyTimeAggregations = (data: any[], aggregations: TimeAggregation[]) => {
		if (aggregations.length === 0) return data;
		// 对于演示目的，我们返回原始数据并添加聚合字段的标记
		return data.map(row => {
			const newRow = { ...row };
			aggregations.forEach(agg => {
				if (agg.newFieldName && isTimeField(agg.field)) {
					// 为时间聚合添加新字段
					const dateValue = row[agg.field];
					if (dateValue && typeof dateValue === 'string') {
						const date = new Date(dateValue);
						switch (agg.timeGrouping) {
							case 'year':
								newRow[agg.newFieldName] = date.getFullYear().toString();
								break;
							case 'month':
								newRow[agg.newFieldName] = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
								break;
							case 'day':
								newRow[agg.newFieldName] = dateValue;
								break;
							default:
								newRow[agg.newFieldName] = dateValue;
						}
					}
				}
			});

			return newRow;
		});
	};



	// 应用表合并逻辑
	const applyTableMerges = (data: any[], mergeConfigs: TableMergeConfig[]) => {
		if (mergeConfigs.length === 0) return data;

		// 简化实现：这里只是示例，实际应用中需要更复杂的表合并逻辑
		// 对于演示目的，我们模拟表合并的效果
		let result = [...data];

		mergeConfigs.forEach(config => {
			if (config.targetTable && config.joinConditions.length > 0) {
				// 模拟目标表数据
				const targetTableData = getMockTableData(config.targetTable);

				// 简单的合并逻辑示例
				result = result.map(row => {
					// 查找匹配的目标表记录
					const matchedRecord = targetTableData.find(targetRow => {
						return config.joinConditions.every(condition => {
							if (condition.sourceField && condition.targetField) {
								return row[condition.sourceField] === targetRow[condition.targetField];
							}
							return false;
						});
					});

					// 根据合并方式处理数据
					if (matchedRecord) {
						// 合并字段（避免字段名冲突）
						const mergedRow = { ...row };
						Object.keys(matchedRecord).forEach(key => {
							const newKey = `${config.targetTable}_${key}`;
							mergedRow[newKey] = matchedRecord[key];
						});
						return mergedRow;
					}

					// 如果是左合并或并集合并，保留原记录
					if (config.mergeType === 'left' || config.mergeType === 'outer') {
						return row;
					}

					// 其他情况返回null，后续过滤
					return null;
				}).filter(row => row !== null);
			}
		});

		return result;
	};



	// 弹窗状态
	const [settingModal, setSettingModal] = useState<{
		open: boolean;
		type: 'row' | 'value' | null;
		idx: number | null;
	}>({ open: false, type: null, idx: null });

	const [settingForm, setSettingForm] = useState<any>({});

	// 打开设置弹窗
	const openSetting = (type: 'row' | 'value', idx: number) => {
		if (type === 'row') {
			setSettingForm({ ...pivotRows[idx] });
		} else {
			setSettingForm({ ...pivotValues[idx] });
		}
		setSettingModal({ open: true, type, idx });
	};

	// 弹窗确认
	const handleSettingOk = () => {
		if (settingModal.type === 'row' && settingModal.idx !== null) {
			const newRows = [...pivotRows];
			newRows[settingModal.idx] = { ...newRows[settingModal.idx], ...settingForm };
			setPivotRows(newRows);
		}
		if (settingModal.type === 'value' && settingModal.idx !== null) {
			const newVals = [...pivotValues];
			newVals[settingModal.idx] = { ...newVals[settingModal.idx], ...settingForm };
			setPivotValues(newVals);
		}
		setSettingModal({ open: false, type: null, idx: null });
	};

	// 弹窗移除
	const handleSettingRemove = () => {
		if (settingModal.type === 'row' && settingModal.idx !== null) {
			setPivotRows(pivotRows.filter((_, i) => i !== settingModal.idx));
		}
		if (settingModal.type === 'value' && settingModal.idx !== null) {
			setPivotValues(pivotValues.filter((_, i) => i !== settingModal.idx));
		}
		setSettingModal({ open: false, type: null, idx: null });
	};

	// 筛选器选中值
	const [filterSelections, setFilterSelections] = useState<{ [field: string]: string }>({});

	// 获取去重枚举值
	const getEnumValues = (field: any) => {
		const values = mockData.map(row => row[field]).filter(v => v !== undefined && v !== null);
		return Array.from(new Set(values));
	};
	// 过滤、排序和聚合后的数据
	const processedData = useMemo(() => {
		// 首先进行过滤
		let result = mockData.filter(row =>
			filters.every(f =>
				!f.field ||
				!filterSelections[f.field] ||
				filterSelections[f.field] === '全部' ||
				row[f.field as keyof typeof row] === filterSelections[f.field]
			)
		);

		// 然后进行排序
		if (sortConditions.length > 0) {
			result = [...result].sort((a, b) => {
				for (const condition of sortConditions) {
					if (!condition.field || !condition.direction) continue;

					const aValue = a[condition.field as keyof typeof a];
					const bValue = b[condition.field as keyof typeof b];

					// 处理不同数据类型的比较
					let comparison = 0;
					if (typeof aValue === 'number' && typeof bValue === 'number') {
						comparison = aValue - bValue;
					} else {
						comparison = String(aValue).localeCompare(String(bValue));
					}

					if (comparison !== 0) {
						return condition.direction === 'asc' ? comparison : -comparison;
					}
				}
				return 0;
			});
		}

		// 然后进行时间聚合
		if (timeAggregations.length > 0) {
			result = applyTimeAggregations(result, timeAggregations);
		}

		// 最后进行表合并
		if (tableMergeConfigs.length > 0) {
			result = applyTableMerges(result, tableMergeConfigs);
		}

		return result;
	}, [mockData, filters, filterSelections, sortConditions, timeAggregations, tableMergeConfigs]);

	// Replace the local data processing with API call
	const { data: pivotData, isLoading, error } = useQuery({
		queryKey: ['pivotData', {
			filters,
			pivotRows,
			pivotColumns,
			pivotValues,
			filterSelections
		}],
		queryFn: async () => {
			// Use your tRPC client to fetch data from backend
			const response = await trpc.getPivotData.query({
				filters: filters.filter(f => f.field),
				rows: pivotRows.filter(r => r.field),
				columns: pivotColumns.filter(c => c.field),
				values: pivotValues.filter(v => v.field),
				filterSelections
			});
			return response;
		},
		enabled: !!(pivotRows.length || pivotColumns.length || pivotValues.length)
	});

	// Replace the local pivotResult with API response
	const pivotResult = useMemo(() => {
		if (!pivotRows.length && !pivotColumns.length && !pivotValues.length) {
			return { columns, data: processedData };
		}

		if (isLoading) {
			return { columns: [], data: [], loading: true };
		}

		if (error) {
			return { columns: [], data: [], error: true };
		}

		return pivotData || { columns: [], data: [] };
	}, [pivotData, isLoading, error, pivotRows, pivotColumns, pivotValues, columns, processedData]);

	const renderOperation = () => {
		switch (operation) {
			case 'pivot':
				return (
					<PivotOperation
						filters={filters}
						setFilters={setFilters}
						pivotColumns={pivotColumns}
						setPivotColumns={setPivotColumns}
						pivotRows={pivotRows}
						setPivotRows={setPivotRows}
						pivotValues={pivotValues}
						setPivotValues={setPivotValues}
						filterSelections={filterSelections}
						setFilterSelections={setFilterSelections}
						getAvailableFields={getAvailableFields}
						getEnumValues={getEnumValues}
						openSetting={openSetting}
					/>
				);
			case 'filter':
				return (
					<FilterOperation
						filterNodes={filterNodes}
						setFilterNodes={setFilterNodes}
						fieldKeys={fieldKeys}
					/>
				);
			case 'merge':
				return (
					<TableMergeOperation
						tableMergeConfigs={tableMergeConfigs}
						setTableMergeConfigs={setTableMergeConfigs}
						availableTables={getAvailableTables()}
						fieldKeys={fieldKeys}
						getTableFields={getTableFields}
					/>
				);
			case 'timeagg':
				return (
					<TimeAggregationOperation
						timeAggregations={timeAggregations}
						setTimeAggregations={setTimeAggregations}
						fieldKeys={fieldKeys}
						isTimeField={isTimeField}
					/>
				);
			case 'sort':
				return (
					<SortOperation
						sortConditions={sortConditions}
						setSortConditions={setSortConditions}
						// fieldKeys={fieldKeys}
						getAvailableFields={getAvailableFieldsForSort}
					/>
				);
			default:
				return null;
		}
	};

	return (
		<div className="flex p-2 flex-1 h-[calc(100vh-64px)]">
			<div className="flex-1 flex flex-col">
				{/* 顶部标题区 */}
				<div className="flex justify-between items-center">
					<div className="text-2xl font-bold">新建视图</div>
					<div className="flex items-center space-x-4 mt-4 mb-4">
						<Button type="text" className="flex flex-col items-center" block onClick={() => {
							if (operation === 'pivot') {
								setOperation('');
							} else {
								setOperation('pivot');
								addOperationRecord('pivot');
							}
						}}>
							<TableOutlined style={{ fontSize: '20px' }} />
							<div className="mt-1 text-xs">数据透视</div>
						</Button>
						<Button type="text" className="flex flex-col items-center" onClick={() => {
							if (operation === 'merge') {
								setOperation('');
							} else {
								setOperation('merge');
								addOperationRecord('merge');
							}
						}}>
							<MergeCellsOutlined style={{ fontSize: '20px' }} />
							<span className="mt-1 text-xs">表合并</span>
						</Button>
						<Button type="text" className="flex flex-col items-center" onClick={() => {
							if (operation === 'timeagg') {
								setOperation('');
							} else {
								setOperation('timeagg');
								addOperationRecord('timeagg');
							}
						}}>
							<FieldTimeOutlined style={{ fontSize: '20px' }} />
							<span className="mt-1 text-xs">时间聚合</span>
						</Button>
						<Button type="text" className="flex flex-col items-center" onClick={() => {
							setFormulaOpen(true);
							addOperationRecord('formula');
						}}>
							<PlusSquareOutlined style={{ fontSize: '20px' }} />
							<span className="mt-1 text-xs">新增计算列</span>
						</Button>
						<Button type="text" className="flex flex-col items-center" onClick={() => {
							if (operation === 'sort') {
								setOperation('');
							} else {
								setOperation('sort');
								addOperationRecord('sort');
							}
						}}>
							<SortAscendingOutlined style={{ fontSize: '20px' }} />
							<span className="mt-1 text-xs">排序</span>
						</Button>
						<Button type="text" className="flex flex-col items-center" onClick={() => {
							if (operation === 'filter') {
								setOperation('');
							} else {
								setOperation('filter');
								addOperationRecord('filter');
							}
						}}>
							<FilterOutlined style={{ fontSize: '20px' }} />
							<span className="mt-1 text-xs">筛选</span>
						</Button>
					</div>
					{/* <div className="flex gap-2"> */}
					<Button type="primary" onClick={() => setCreateModalOpen(true)}>保存</Button>
					{/* </div> */}
				</div>
				<div className="flex gap-2 flex-1 overflow-hidden">
					<Splitter layout="vertical" lazy className="flex flex-1 gap-2 h-[calc(100vh-120px)]">
						{/* 操作区域 - 上半部分 */}
						{
							operation && <Splitter.Panel defaultSize="40%" className="overflow-y-auto h-[calc(35vh)]">
								{renderOperation()}
							</Splitter.Panel>
						}
						{/* 主表格区 - 下半部分 */}
						<Splitter.Panel className="flex flex-col flex-1 gap-2">
							{/* 列设置按钮 */}
							<div className="flex justify-end">
								<Button
									size="small"
									onClick={() => setColModalOpen(true)}
								>
									列设置
								</Button>
							</div>
							<Table
								dataSource={pivotResult.data}
								columns={pivotResult.columns}
								pagination={false}
								bordered
								className="flex-1"
								scroll={{ x: "max-content" }}
								loading={pivotResult.loading}
								locale={{
									emptyText: pivotResult.error
										? '获取数据失败，请重试'
										: pivotResult.data.length === 0
											? '暂无数据，请调整筛选或数据透视配置'
											: undefined
								}}
							/>
						</Splitter.Panel>
					</Splitter>

					{/* 右侧操作记录区 */}
					<div className="w-1/5 flex flex-col pl-4">
						<div className="font-bold text-base mb-2">操作记录</div>
						<Timeline
							className="overflow-y-auto h-[calc(100vh-200px)]"
							items={operationRecords.map((record) => ({
								dot: record.icon,
								color: record.isActive ? 'blue' : 'gray',
								children: (
									<span
										className={`flex items-center justify-between cursor-pointer p-2 rounded group hover:bg-gray-50 ${record.isActive ? 'bg-blue-50 border-l-2 border-blue-500' : ''
											}`}
										onClick={() => switchToOperationRecord(record.id)}
									>
										<div className="flex flex-col">
											<span className={`text-sm ${record.isActive ? 'font-medium text-blue-600' : 'text-gray-700'}`}>
												{record.name}
											</span>
											<span className="text-xs text-gray-400">
												{record.timestamp.toLocaleTimeString()}
											</span>
										</div>
										<div className="invisible group-hover:visible">
											<DeleteOutlined
												className="ml-2 cursor-pointer text-red-500"
												onClick={(e) => {
													e.stopPropagation();
													handleDeleteOperationRecord(record.id);
												}}
											/>
										</div>
									</span>
								),
							}))}
						/>
					</div>
				</div>
			</div>
			{/* 列设置弹窗 */}
			<Modal
				title="列设置"
				open={colModalOpen}
				onCancel={() => setColModalOpen(false)}
				onOk={() => {
					setColumns(allColumns.filter(col => checkedCols.includes(col.key)))
					setColModalOpen(false)
				}}
				okText="确定"
				cancelText="取消"
			>
				<Checkbox.Group
					value={checkedCols}
					onChange={handleColChange}
					className="flex flex-col gap-2"
				>
					{allColumns.map(col => (
						<Checkbox key={col.key} value={col.key}>
							{col.title}
						</Checkbox>
					))}
				</Checkbox.Group>
			</Modal>
			{/* 新建视图弹窗 */}
			<Modal
				title="新建视图"
				open={createModalOpen}
				onCancel={() => setCreateModalOpen(false)}
				onOk={() => setCreateModalOpen(false)}
				okText="确定"
				cancelText="取消"
			>
				<Form form={form} layout="vertical">
					<Form.Item label="合并名称" name="name" initialValue="自定义视图名称1">
						<Input />
					</Form.Item>
					<Form.Item label="存放位置" name="location">
						<Input placeholder="请选择新建视图存放的位置" />
					</Form.Item>
				</Form>
				<div className="text-xs text-gray-400 mt-2">
					存放位置若不选择文件夹，则默认当前文件夹，单独输入文件名，点击确定，直接保存在该文件夹
				</div>
			</Modal>
			{/* 确认删除弹窗 */}
			<Modal
				title="确认删除？"
				open={deleteModalOpen}
				onCancel={() => setDeleteModalOpen(false)}
				onOk={() => setDeleteModalOpen(false)}
				okText="确定"
				cancelText="取消"
			>
				<div className="text-base mb-4">删除后无法恢复</div>
			</Modal>
			{/* 设置弹窗 */}
			<Modal
				open={settingModal.open}
				title={settingModal.type === 'row' ? '行设置' : '值设置'}
				onCancel={() => setSettingModal({ open: false, type: null, idx: null })}
				onOk={handleSettingOk}
				footer={[
					<Button key="remove" danger onClick={handleSettingRemove}>移除字段</Button>,
					<Button key="cancel" onClick={() => setSettingModal({ open: false, type: null, idx: null })}>取消</Button>,
					<Button key="ok" type="primary" onClick={handleSettingOk}>确定</Button>
				]}
			>
				{/* 排序设置 */}
				<div className="mb-2">
					<div className="mb-1 font-bold">排序</div>
					<Radio.Group
						value={settingForm.sort ?? null}
						onChange={e => setSettingForm((f: any) => ({ ...f, sort: e.target.value }))}
						options={[
							{ label: '升序', value: 'asc' },
							{ label: '降序', value: 'desc' },
							{ label: '不排序', value: null },
						]}
						optionType="button"
						buttonStyle="solid"
					/>
				</div>
				{/* 汇总设置*/}
				{/* {settingModal.type === 'value' && ( */}
				<div className="mb-2">
					<div className="mb-1 font-bold">汇总</div>
					<Select
						value={settingForm.agg}
						onChange={agg => setSettingForm((f: any) => ({ ...f, agg }))}
						style={{ width: 180 }}
						options={[
							{ label: '求和', value: 'sum' },
							{ label: '计数', value: 'count' },
							{ label: '平均值', value: 'avg' },
							{ label: '最大值', value: 'max' },
							{ label: '最小值', value: 'min' },
							{ label: '第一个值', value: 'first' },
							{ label: '最后一个值', value: 'last' },
							{ label: '中位数', value: 'median' },
							{ label: '字符拼接', value: 'join' }
						]}
					/>
					{/* 分隔符输入，仅字符拼接时 */}
					{settingForm.agg === 'join' && (
						<div className="mt-2">
							<div className="text-xs text-gray-500 mb-1">分隔符（默认为逗号）：</div>
							<Input
								value={settingForm.joinSeparator ?? ','}
								onChange={e => setSettingForm((f: any) => ({ ...f, joinSeparator: e.target.value }))}
								placeholder="输入分隔符，如：, | ; 等"
								style={{ width: 180 }}
							/>
						</div>
					)}
				</div>
				{/* )} */}
			</Modal>
			{/* 新增计算列的公式编辑器 */}
			<FormulaEditor
				open={formulaOpen}
				setOpen={setFormulaOpen}
			/>
		</div>
	)
}
export default ViewDetail




