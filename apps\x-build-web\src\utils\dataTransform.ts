import { CustomTreeNode } from '../types';

// tRPC返回的数据结构类型
type TRPCDataSourceCatalog = Record<string, Array<{
  name: string;
  description: string | null;
  data_entities: Array<{
    id: string;
    name: string;
    description: string | null;
  }>;
}>>;

/**
 * 将tRPC返回的数据源目录转换为CustomTreeNode格式
 * @param catalog tRPC返回的数据源目录
 * @returns CustomTreeNode数组，用于Tree组件渲染
 */
export function transformDataSourceCatalogToTreeNodes(catalog: TRPCDataSourceCatalog): CustomTreeNode[] {
  const treeNodes: CustomTreeNode[] = [];
  
  // 遍历每个数据源类型（如"数据库"、"本地文件"等）
  Object.entries(catalog).forEach(([sourceType, datasets], folderIndex) => {
    const folderNode: CustomTreeNode = {
      title: sourceType,
      key: `folder_${sourceType}_${folderIndex}`,
      type: 'folder',
      editable: false,
      usedInWorkspace: false,
      children: []
    };

    // 遍历该类型下的所有数据集
    datasets.forEach((dataset, datasetIndex) => {
      // 为每个数据集创建子节点（表）
      dataset.data_entities.forEach((entity, entityIndex) => {
        const tableNode: CustomTreeNode = {
          title: entity.name,
          key: `table_${entity.id}`,
          type: 'table',
          tableType: getTableTypeFromSourceType(sourceType),
          editable: false,
          usedInWorkspace: false, // 这里可以根据实际业务逻辑判断
          entityId: entity.id, // 保存实体ID用于后续查询
          datasetName: dataset.name,
          description: entity.description
        };
        
        folderNode.children!.push(tableNode);
      });
    });

    // 只有当文件夹有子节点时才添加到树中
    if (folderNode.children!.length > 0) {
      treeNodes.push(folderNode);
    }
  });

  return treeNodes;
}

/**
 * 根据数据源类型确定表类型
 * @param sourceType 数据源类型名称
 * @returns 表类型
 */
function getTableTypeFromSourceType(sourceType: string): 'db' | 'excel' {
  switch (sourceType) {
    case '数据库':
      return 'db';
    case '本地文件':
    case '远程文件':
      return 'excel';
    case '采集器':
      return 'db'; // 采集器数据通常存储在数据库中
    default:
      return 'excel';
  }
}

/**
 * 将视图数据转换为CustomTreeNode格式
 * @param views 视图数据数组
 * @returns CustomTreeNode数组
 */
export function transformViewsToTreeNodes(views: Array<{
  name: string;
  description: string | null;
  data_entities: Array<{
    id: string;
    name: string;
    description: string | null;
  }>;
}>): CustomTreeNode[] {
  // 创建默认的视图文件夹
  const viewFolder: CustomTreeNode = {
    title: '视图文件夹',
    key: 'view_folder_default',
    type: 'folder',
    editable: false,
    usedInWorkspace: false,
    children: []
  };

  // 将所有视图添加到默认文件夹中
  views.forEach((view) => {
    view.data_entities.forEach((entity) => {
      const viewNode: CustomTreeNode = {
        title: entity.name,
        key: `view_${entity.id}`,
        type: 'table',
        editable: false,
        usedInWorkspace: false, // 根据实际业务逻辑判断
        entityId: entity.id,
        datasetName: view.name,
        description: entity.description
      };
      
      viewFolder.children!.push(viewNode);
    });
  });

  return viewFolder.children!.length > 0 ? [viewFolder] : [];
}
