// import { createTRPCClient, httpBatchLink } from '@trpc/client';
import type { AppRouter } from '@server/@generated/server';
import { QueryClient } from '@tanstack/react-query';
import { createTRPCClient, httpBatchLink } from '@trpc/client';
import { createTRPCOptionsProxy } from '@trpc/tanstack-react-query';
export const queryClient = new QueryClient({
	defaultOptions: {
		queries: {
			refetchOnWindowFocus: false,
			refetchOnMount: false,
			refetchOnReconnect: false,
			retry:false
		},
	},
});
const trpcClient = createTRPCClient<AppRouter>({
	links: [httpBatchLink({ url: 'http://localhost:3000/' })],
});
export const trpc = createTRPCOptionsProxy<AppRouter>({
	client: trpcClient,
	queryClient,
});