{"name": "x-build-server", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "cli": "node dist/cli"}, "dependencies": {"@astronautlabs/jsonpath": "^1.1.2", "@aws-sdk/client-s3": "^3.837.0", "@aws-sdk/s3-request-presigner": "^3.837.0", "@duckdb/node-api": "1.3.1-alpha.22", "@keycloak/keycloak-admin-client": "^26.2.0", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^11.0.20", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/platform-express": "^11.0.1", "@prisma/client": "^6.10.1", "axios": "^1.8.4", "js-yaml": "^4.1.0", "keycloak-connect": "^26.1.1", "nanoid": "^5.1.5", "ndjson": "^2.0.0", "nest-commander": "^3.17.0", "nest-keycloak-connect": "^1.10.1", "nestjs-trpc": "^1.6.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "uuid": "^11.1.0", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.20", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/js-yaml": "^4.0.9", "@types/ndjson": "^2.0.4", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "eslint": "^9.24.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "openapi-types": "^12.1.3", "prettier": "^3.4.2", "prisma": "^6.10.1", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}