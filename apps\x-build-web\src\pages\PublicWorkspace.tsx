import { Key, ReactNode, useEffect, useRef, useState } from 'react';
import { Tabs, Button, Input, Tree, Modal, message, Dropdown, Space, Flex, CollapseProps, Collapse, Spin, Alert } from 'antd';
import { MoreOutlined, PlusOutlined, SearchOutlined, ExclamationCircleOutlined, DeleteOutlined } from '@ant-design/icons';
import { useQuery } from '@tanstack/react-query';
import DataPreview from '../components/DataPreview';
import RelationView from '../components/RelationView';
// import FormulaEditorModal from '../components/FormulaEditorModal';
import DatabaseTableCreateModal from '../components/DatabaseTableCreateModal';
import DatabaseTableEditModal from '../components/DatabaseTableEditModal';
import { CustomTreeNode, DatabaseInfo, Dataset } from '../types';
import { findNodeByKey, tableMenu, updateNodeByKey } from '../utils';
// import { trpc } from '../service/tRPC';
import { useNavigate } from 'react-router';
import { useTRPC } from '../service/tRPC';

// import { useNavigate } from 'react-router-dom';

// 构建数据源目录树形结构
const buildTreeFromDataSourceCatalog = (catalog: Record<string, Dataset[]>): CustomTreeNode[] => {
	const treeNodes: CustomTreeNode[] = [];

	Object.entries(catalog).forEach(([sourceType, datasets], folderIndex) => {
		const folderNode: CustomTreeNode = {
			title: sourceType,
			key: `folder_${sourceType}_${folderIndex}`,
			name: sourceType,
			type: 'folder',
			editable: false,
			usedInWorkspace: false,
			children: []
		};

		datasets.forEach((dataset) => {
			dataset.data_entities.forEach((entity) => {
				const tableNode: CustomTreeNode = {
					title: entity.name,
					key: `table_${entity.id}`,
					name: entity.name,
					description: entity.description,
					type: 'table',
					tableType: getTableTypeFromSourceType(sourceType),
					editable: false,
					usedInWorkspace: false,
					entityId: entity.id,
					datasetName: dataset.name
				};

				folderNode.children!.push(tableNode);
			});
		});

		if (folderNode.children!.length > 0) {
			treeNodes.push(folderNode);
		}
	});

	return treeNodes;
};

// 构建视图树形结构
const buildTreeFromViews = (views: Dataset[]): CustomTreeNode[] => {
	const viewFolder: CustomTreeNode = {
		title: '视图文件夹',
		key: 'view_folder_default',
		name: '视图文件夹',
		type: 'folder',
		editable: false,
		usedInWorkspace: false,
		children: []
	};

	views.forEach((view) => {
		view.data_entities.forEach((entity) => {
			const viewNode: CustomTreeNode = {
				title: entity.name,
				key: `view_${entity.id}`,
				name: entity.name,
				description: entity.description,
				type: 'table',
				editable: false,
				usedInWorkspace: false,
				entityId: entity.id,
				datasetName: view.name
			};

			viewFolder.children!.push(viewNode);
		});
	});

	return viewFolder.children!.length > 0 ? [viewFolder] : [];
};

// 根据数据源类型确定表类型
const getTableTypeFromSourceType = (sourceType: string): 'db' | 'excel' => {
	switch (sourceType) {
		case '数据库':
			return 'db';
		case '本地文件':
		case '远程文件':
			return 'excel';
		case '采集器':
			return 'db';
		default:
			return 'excel';
	}
};

const PublicWorkSpace = () => {
	const [activeTab, setActiveTab] = useState('dataPreview');
	const [activeTree, setActiveTree] = useState('data');
	const [expandedKeys, setExpandedKeys] = useState<Key[]>([]);
	const [selectedKey, setSelectedKey] = useState<Key>('');
	// 视图相关状态
	const [viewExpandedKeys, setViewExpandedKeys] = useState<Key[]>([]);
	const [viewSelectedKeys, setViewSelectedKeys] = useState<Key[]>([]);
	const [addKey, setAddKey] = useState('')
	const navigate = useNavigate()
	const [editingKey, setEditingKey] = useState<string | null>(null);
	const [editingValue, setEditingValue] = useState<string>('');
	const [createDbModalOpen, setCreateDbModalOpen] = useState(false);
	const [editDbModalOpen, setEditDbModalOpen] = useState(false);
	const trpc = useTRPC();
	// 使用tRPC查询数据源表
	const { data: tablesData, isLoading: tablesLoading, error: tablesError } = useQuery(
		trpc.dataSource.listTables.queryOptions({ workspaceId: null })
	);

	// 使用tRPC查询视图数据
	const { data: viewsData, isLoading: viewsLoading, error: viewsError } = useQuery(
		trpc.dataSource.listViews.queryOptions({ workspaceId: null })
	);

	// 直接使用后端数据构建树形结构
	const treeData: CustomTreeNode[] = tablesData ? buildTreeFromDataSourceCatalog(tablesData) : [];
	const transformedViewTreeData: CustomTreeNode[] = viewsData ? buildTreeFromViews(viewsData) : [];

	// 使用tRPC数据或默认数据初始化viewTreeData
	const [viewTreeData, setViewTreeData] = useState<CustomTreeNode[]>([]);

	// 当视图数据加载完成时更新viewTreeData
	useEffect(() => {
		if (transformedViewTreeData.length > 0) {
			setViewTreeData(transformedViewTreeData);
		}
	}, [transformedViewTreeData]);

	const [editDbInitialValues, setEditDbInitialValues] = useState<DatabaseInfo>({
		name: '',
		type: '',
		host: '',
		port: 0,
		username: '',
		password: ''
	});
	const handleAddDataTable = (folderKey: string) => {
		const newTable: CustomTreeNode = {
			title: `新表-数据库`,
			key: `${folderKey}-new-${Date.now()}`,
			type: 'table',
			tableType: 'db',
			editable: false,
			usedInWorkspace: false,
		};
		setTreeDataState(prev => updateNodeByKey(prev, folderKey, node => {
			node.children = node.children ? [...node.children, newTable] : [newTable];
		}));
		setAddKey('')
	}
	// 视图选择处理函数
	const handleViewSelect = (selectedKeys: React.Key[]) => {
		setViewSelectedKeys(selectedKeys);
	};

	const handleTableSelect = (selectedKeys: React.Key[]) => {
		setSelectedKey(selectedKeys[0] as string);
	};
	const handleTabChange = (key: string) => {
		setActiveTab(key);
	};

	// 重命名保存
	const handleRenameSave = (key: string) => {
		if (!editingValue.trim()) {
			message.warning('名称不能为空');
			return;
		}
		treeDataRef.current = updateNodeByKey(treeDataRef.current, key, node => {
			node.title = editingValue;
			node.editable = false;
		});
		setTreeDataState([...treeDataRef.current]);
		setEditingKey(null);
		setEditingValue('');
	};
	const handleRenameView = (key: string) => {
		if (!editingValue.trim()) {
			message.warning('名称不能为空');
			return;
		}
		setViewTreeData(updateNodeByKey(viewTreeData, key, node => {
			node.title = editingValue;
			node.editable = false;
		}));
		setEditingKey(null);
		setEditingValue('');
	};
	const removeNode = (nodes: CustomTreeNode[], key: string): CustomTreeNode[] => {
		return nodes.filter(node => {
			if (node.key === key) return false;
			if (node.children) node.children = removeNode(node.children, key);
			return true;
		});
	};
	// 删除节点
	const handleDeleteView = (key: string) => {
		const node = findNodeByKey(viewTreeData, key);
		if (!node) return;
		if (node.type === 'folder') {
			const hasUsed = (node.children || []).some(child => child.usedInWorkspace);
			if (hasUsed) {
				Modal.warning({
					title: '无法删除',
					content: '当前文件夹中有表在工作区中被使用，无法删除！',
				});
				return;
			}
		}
		if (node.type === 'table' && node.usedInWorkspace) {
			Modal.warning({
				title: '无法删除',
				content: '当前表在工作区被使用，请先删除工作区中相关表，再删除该表！',
			});
			return;
		}
		Modal.confirm({
			title: '确认删除',
			content: `是否删除${node.type === 'folder' ? '文件夹' : '表'}"${typeof node.title === 'string' ? node.title : ''}"？`,
			onOk: () => {
				setViewTreeData(removeNode(viewTreeData, key));
			},
		});
	};
	const handleDelete = (key: string) => {
		const node = findNodeByKey(treeData, key);
		if (!node) return;
		if (node.type === 'folder') {
			const hasUsed = (node.children || []).some(child => child.usedInWorkspace);
			if (hasUsed) {
				Modal.warning({
					title: '无法删除',
					content: '当前文件夹中有表在工作区中被使用，无法删除！',
				});
				return;
			}
		}
		if (node.type === 'table' && node.usedInWorkspace) {
			Modal.warning({
				title: '无法删除',
				content: '当前表在工作区被使用，请先删除工作区中相关表，再删除该表！',
			});
			return;
		}
		Modal.confirm({
			title: '确认删除',
			content: `是否删除${node.type === 'folder' ? '文件夹' : '表'}"${typeof node.title === 'string' ? node.title : ''}"？`,
			onOk: () => {
				const removeNode = (nodes: CustomTreeNode[], key: string): CustomTreeNode[] => {
					return nodes.filter(node => {
						if (node.key === key) return false;
						if (node.children) node.children = removeNode(node.children, key);
						return true;
					});
				};
				setTreeDataState(removeNode(treeDataState, key));
			},
		});
	};
	// treeData状态管理（便于递归操作）
	const [treeDataState, setTreeDataState] = useState<CustomTreeNode[]>([]);
	const treeDataRef = useRef(treeDataState);
	useEffect(() => { treeDataRef.current = treeDataState; }, [treeDataState]);

	// 当tRPC数据加载完成时更新treeDataState
	useEffect(() => {
		if (treeData.length > 0) {
			setTreeDataState(treeData);
		}
	}, [treeData]);

	const handleAddTable = (folderKey: string, type: 'db' | 'excel') => {
		if (type === 'db') {
			setCreateDbModalOpen(true)
		} else {
			// 跳转到文件上传页面
			navigate('/publicWorkspace/upload')
		}
		setAddKey(folderKey)
	};
	const handleMoreActions = (nodeData: CustomTreeNode, type: 'rename' | 'delete') => {
		if (type === 'rename') {
			setEditingKey(nodeData.key as string);
			setEditingValue(typeof nodeData.title === 'string' ? nodeData.title : '');
		} else {
			handleDelete(nodeData.key as string)
		}
	}
	const handleMoreViewActions = (nodeData: CustomTreeNode, type: 'rename' | 'delete') => {
		if (type === 'rename') {
			setEditingKey(nodeData.key as string);
			setEditingValue(typeof nodeData.title === 'string' ? nodeData.title : '');
		} else {
			handleDeleteView(nodeData.key as string)
		}
	}
	// titleRender实现
	const renderCustomNodeTitle = (nodeData: CustomTreeNode, tableType: 'db' | 'excel') => {
		if (editingKey === nodeData.key) {
			return (
				<Input
					autoFocus
					size="small"
					value={editingValue}
					onChange={e => setEditingValue(e.target.value)}
					onBlur={() => handleRenameSave(nodeData.key as string)}
					onPressEnter={() => handleRenameSave(nodeData.key as string)}
					className="w-full"
				/>
			);
		}
		// 文件夹节点
		if (nodeData.type === 'folder') {
			return (
				<div className="flex items-center justify-between">
					<span onDoubleClick={() => {
						setEditingKey(nodeData.key as string);
						setEditingValue(typeof nodeData.title === 'string' ? nodeData.title : '');
					}}>{nodeData.title as ReactNode}</span>
					<Space>
						<Button type="text" size="small" onClick={e => { e.stopPropagation(); handleAddTable(nodeData.key as string, tableType); }} icon={<PlusOutlined />} />
						<Button type="text" size="small" onClick={e => { e.stopPropagation(); handleDelete(nodeData.key as string); }} icon={<DeleteOutlined />} />
					</Space>
				</div>
			);
		}
		// 表节点
		if (nodeData.type === 'table') {
			return (
				<div className="flex items-center group justify-between">
					<span
						style={{
							color: nodeData.usedInWorkspace ? '#faad14' : undefined,
							fontWeight: nodeData.usedInWorkspace ? 600 : undefined,
							marginRight: 4,
						}}
						onDoubleClick={() => {
							setEditingKey(nodeData.key as string);
							setEditingValue(typeof nodeData.title === 'string' ? nodeData.title : '');
						}}
					>
						{nodeData.title as ReactNode}
						{nodeData.tableType ? <span style={{ fontSize: 12, color: '#888', marginLeft: 4 }}>({nodeData.tableType})</span> : null}
						{nodeData.usedInWorkspace && <ExclamationCircleOutlined style={{ color: '#faad14', marginLeft: 4 }} title="被工作区使用" />}
					</span>
					<span className="invisible group-hover:visible">
						<Dropdown
							menu={{
								items: tableMenu,
								onClick: ({ key }) => handleMoreActions(nodeData, key as 'rename' | 'delete')
							}}
							trigger={['hover']}
						>
							<MoreOutlined className="ml-2 cursor-pointer" />
						</Dropdown>
					</span>
				</div>
			);
		}
		// 其他情况
		return <span>{nodeData.title as ReactNode}</span>;
	};
	const renderCustomViewTitle = (nodeData: CustomTreeNode) => {
		if (editingKey === nodeData.key) {
			return (
				<Input
					autoFocus
					size="small"
					value={editingValue}
					onChange={e => setEditingValue(e.target.value)}
					onBlur={() => handleRenameView(nodeData.key as string)}
					onPressEnter={() => handleRenameView(nodeData.key as string)}
					className="w-full"
				/>
			);
		}
		// 文件夹节点
		if (nodeData.type === 'folder') {
			return (
				<div className="flex items-center justify-between">
					<span onDoubleClick={() => {
						setEditingKey(nodeData.key as string);
						setEditingValue(typeof nodeData.title === 'string' ? nodeData.title : '');
					}}>{nodeData.title as ReactNode}</span>
					<Button type="text" size="small" onClick={e => { e.stopPropagation(); handleDeleteView(nodeData.key as string); }} icon={<DeleteOutlined />} />
				</div>
			);
		}
		// 表节点
		if (nodeData.type === 'table') {
			return (
				<div className="flex group items-center justify-between">
					<span
						style={{
							color: nodeData.usedInWorkspace ? '#faad14' : undefined,
							fontWeight: nodeData.usedInWorkspace ? 600 : undefined,
							marginRight: 4,
						}}
						onDoubleClick={() => {
							setEditingKey(nodeData.key as string);
							setEditingValue(typeof nodeData.title === 'string' ? nodeData.title : '');
						}}
					>
						{nodeData.title as ReactNode}
						{nodeData.tableType ? <span style={{ fontSize: 12, color: '#888', marginLeft: 4 }}>({nodeData.tableType})</span> : null}
						{nodeData.usedInWorkspace && <ExclamationCircleOutlined style={{ color: '#faad14', marginLeft: 4 }} title="被工作区使用" />}
					</span>
					{/* <span className="opacity-0 group-hover:opacity-100 transition-opacity"> */}
					<span className="invisible group-hover:visible">
						<Dropdown
							menu={{
								items: tableMenu,
								onClick: ({ key }) => handleMoreViewActions(nodeData, key as 'rename' | 'delete')
							}}
							trigger={['hover']}
						>
							<MoreOutlined className="ml-2 cursor-pointer" />
						</Dropdown>
					</span>
				</div>
			);
		}
		// 其他情况
		return <span>{nodeData.title as ReactNode}</span>;
	};
	const collapseItems: CollapseProps['items'] = [
		{
			key: 'get',
			label: '采集器',
			children: <Tree
				// className="h-36 overflow-y-auto"
				treeData={treeDataState}
				// expandedKeys={expandedKeys}
				onSelect={handleTableSelect}
				// onExpand={setExpandedKeys}
				showLine={{ showLeafIcon: false }}
				blockNode
			/>,
		},
		{
			key: 'db',
			label: '数据库',
			children: <Tree
				// className="h-36 flex-1 overflow-y-auto"
				treeData={treeDataState}
				// expandedKeys={expandedKeys}
				onSelect={handleTableSelect}
				// onExpand={setExpandedKeys}
				showLine={{ showLeafIcon: false }}
				blockNode
				titleRender={(nodeData) => renderCustomNodeTitle(nodeData, 'db')}
			/>,
		},
		{
			key: 'excel',
			label: '本地文件',
			children: <Tree
				// className="h-36 flex-1 overflow-y-auto"
				treeData={treeDataState}
				expandedKeys={expandedKeys}
				onSelect={handleTableSelect}
				onExpand={setExpandedKeys}
				showLine={{ showLeafIcon: false }}
				blockNode
				titleRender={(nodeData) => renderCustomNodeTitle(nodeData, 'excel')}
			/>,
		},
	];

	// 新建文件夹功能
	const handleAddFolder = () => {
		// 统计已有文件夹数量
		const folderCount = treeDataState.filter(node => node.type === 'folder').length;
		const newFolder = {
			title: `文件夹${folderCount + 1}`,
			key: `folder${Date.now()}`,
			type: 'folder',
			editable: false,
			usedInWorkspace: false,
			children: [],
		};
		setTreeDataState(prev => [...prev, newFolder]);
	};

	// 新建视图文件夹功能
	const handleAddViewFolder = () => {
		const folderCount = viewTreeData.length;
		const newFolder: CustomTreeNode = {
			title: `文件夹${folderCount + 1}`,
			key: `viewFolder${Date.now()}`,
			type: 'folder',
			editable: false,
			usedInWorkspace: false,
			children: [],
		};
		setViewTreeData(prev => [...prev, newFolder]);
	};

	// 显示加载状态
	if (tablesLoading || viewsLoading) {
		return (
			<div className="flex h-[calc(100vh-64px)] items-center justify-center">
				<Spin size="large" />
			</div>
		);
	}

	// 显示错误状态
	if (tablesError || viewsError) {
		return (
			<div className="flex h-[calc(100vh-64px)] items-center justify-center">
				<Alert
					message="数据加载失败"
					description={tablesError?.message || viewsError?.message || '请检查网络连接或联系管理员'}
					type="error"
					showIcon
				/>
			</div>
		);
	}

	return (
		<div className="flex h-[calc(100vh-64px)]">
			{/* <FormulaEditorModal open={formulaOpen} onOk={() => setFormulaOpen(false)} onCancel={() => setFormulaOpen(false)} /> */}
			<div className="w-80 bg-white border-r border-gray-200 flex flex-col p-2">
				{/* <div className="flex-1 flex flex-col"> */}
				<Tabs
					className={'rp-[.ant-tabs-content-holder]:flex rp-[.ant-tabs-content-top]:flex flex-1'}
					activeKey={activeTree}
					centered
					onChange={(key: string) => setActiveTree(key)}
					items={[
						{
							key: 'data',
							label: '数据',
							children: (
								<Flex vertical gap={'small'} className={'flex-1 h-full'}>
									<Input
										placeholder="请输入搜索内容"
										prefix={<SearchOutlined />}
									/>
									<div className="flex overflow-y-auto h-[calc(100vh-210px)]">
										<Collapse
											size="small"
											ghost
											className="flex-1"
											items={collapseItems}
										/>
									</div>
									<Button type='text' icon={<PlusOutlined />} onClick={handleAddFolder} className="w-full">
										新建文件夹
									</Button>
								</Flex>
							),
							className: 'flex flex-1',
						},
						{
							key: 'view',
							label: '视图',
							children: (
								<Flex vertical gap={'small'} className={'flex-1 h-full'}>
									<Input
										placeholder="请输入搜索内容"
										prefix={<SearchOutlined />}
									/>
									<Tree
										className="h-[calc(100vh-200px)] flex-1 overflow-y-auto"
										treeData={viewTreeData}
										expandedKeys={viewExpandedKeys}
										selectedKeys={viewSelectedKeys}
										onSelect={handleViewSelect}
										onExpand={setViewExpandedKeys}
										showLine={{ showLeafIcon: false }}
										titleRender={(nodeData) => renderCustomViewTitle(nodeData)}
										blockNode
									/>
									<Button type='text' icon={<PlusOutlined />} onClick={handleAddViewFolder} className="w-full">
										新建文件夹
									</Button>
								</Flex>
							),
							className: 'flex flex-1',
						}
					]}
				/>
				{/* </div> */}
			</div>
			<div className="flex-1 mx-2 flex flex-col">
				<div className="flex justify-between items-center my-2">
					<Space>
						<Button onClick={() => {
							navigate('/publicWorkspace/addView')
						}}>
							新建视图
						</Button>
						{
							activeTree === 'view' &&
							<Button>
								编辑视图
							</Button>
						}
					</Space>
					{
						activeTree === 'data' &&
						<Button onClick={() => {
							setEditDbInitialValues({
								name: '数据库名称',
								type: 'mysql',
								host: '主机',
								port: 0,
								username: '用户名',
								password: '密码',
							});
							setEditDbModalOpen(true);
						}}>
							编辑表
						</Button>
					}
				</div>
				<Tabs
					activeKey={activeTab}
					// centered
					className={'rp-[.ant-tabs-content-holder]:flex rp-[.ant-tabs-content-top]:flex flex-1'}
					onChange={handleTabChange}
					items={[
						{
							key: 'dataPreview',
							label: '数据预览',
							children: <DataPreview />,
							className: 'flex flex-1'
						},
						{
							key: 'relatedView',
							label: '关联视图',
							children: <RelationView />,
							className: 'flex flex-1'
						}
					]}
				/>
			</div>
			<DatabaseTableCreateModal
				open={createDbModalOpen}
				onOk={() => {
					handleAddDataTable(addKey);
					setCreateDbModalOpen(false);
					message.success('创建成功');
				}}
				onCancel={() => {
					setAddKey('')
					setCreateDbModalOpen(false)
				}}
				onTestConnection={() => message.success('连接成功')}
			/>
			<DatabaseTableEditModal
				open={editDbModalOpen}
				initialValues={editDbInitialValues}
				onOk={() => { setEditDbModalOpen(false); message.success('编辑成功'); }}
				onCancel={() => setEditDbModalOpen(false)}
				onTestConnection={() => message.success('连接成功')}
			/>

		</div>
	);
};

export default PublicWorkSpace;
