generator client {
  provider = "prisma-client-js"
  // output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Tenant {
  id          String      @id @db.Uuid
  name        String      @db.VarChar(100)
  created_at  DateTime    @default(now()) @db.Timestamptz()
  updated_at  DateTime    @updatedAt @db.Timestamptz()
  app_domains AppDomain[]
  users       User[]

  @@map("tenants")
}

model User {
  id         String     @id @db.VarChar(100)
  name       String     @db.VarChar(100)
  created_at DateTime   @default(now()) @db.Timestamptz()
  updated_at DateTime   @updatedAt @db.Timestamptz()
  tenant_id  String     @db.Uuid
  tenant     Tenant     @relation(fields: [tenant_id], references: [id])
  user_roles UserRole[]

  @@map("users")
}

model Role {
  id         String     @id @db.Uuid
  name       String?    @db.VarChar(100)
  role_type  RoleType
  created_at DateTime   @default(now()) @db.Timestamptz()
  updated_at DateTime   @updatedAt @db.Timestamptz()
  user_role  UserRole[]

  @@map("roles")
}

model UserRole {
  user_id    String   @db.VarChar(100)
  role_id    String   @db.Uuid
  created_at DateTime @default(now()) @db.Timestamptz()
  user       User     @relation(fields: [user_id], references: [id])
  role       Role     @relation(fields: [role_id], references: [id])

  @@id([user_id, role_id])
  @@map("user_roles")
}

model Permission {
  id               String      @id @db.Uuid
  resouce_selector Json
  resource_type    ResouceType
  code             String      @db.VarChar(100)
  created_at       DateTime    @default(now()) @db.Timestamptz()
  updated_at       DateTime    @updatedAt @db.Timestamptz()

  @@map("permissions")
}

model UserPermission {
  user_id       String   @db.VarChar(100)
  permission_id String   @db.Uuid
  created_at    DateTime @default(now()) @db.Timestamptz()

  @@id([user_id, permission_id])
  @@map("user_permissions")
}

// model DataSource {
//   id            String         @id @db.Uuid
//   name          String         @db.VarChar(100)
//   created_at    DateTime       @default(now()) @db.Timestamptz()
//   updated_at    DateTime       @updatedAt @db.Timestamptz()
//   source_type   DataSourceType
//   config        Json
//   update_policy Json?
//   data_entities DataEntity[]
//   datasets      Dataset[]

//   @@map("data_sources")
// }

model Dataset {
  id            String         @id @db.Uuid
  name          String         @db.VarChar(100)
  namespace     String         @default(nanoid(8)) @db.VarChar(8) //用于生成数据库隔离用的schema名称 @map("unique_name")
  description   String?
  created_at    DateTime       @default(now()) @db.Timestamptz()
  updated_at    DateTime       @updatedAt @db.Timestamptz()
  source_type   DataSourceType
  source_config Json?
  workspace_id  String         @db.Uuid
  workspace     Workspace      @relation(fields: [workspace_id], references: [id])
  deprecated    Boolean        @default(false)
  // data_source_id String       @db.Uuid
  // data_source    DataSource   @relation(fields: [data_source_id], references: [id])
  data_entities DataEntity[]

  @@unique([namespace])
  @@map("datasets")
}

model DataEntity {
  id                      String                @id @db.Uuid
  name                    String                @db.VarChar(100)
  source_path             String?               @db.VarChar(500) //连接远程数据库时，用于标识原有数据库中对应的表名称, 对于本地文件,则对应文件路径, 对于S3/ftp 对应于远程系统的文件路径
  description             String?
  created_at              DateTime              @default(now()) @db.Timestamptz()
  updated_at              DateTime              @updatedAt @db.Timestamptz()
  entity_type             EntityType
  structure               Json //存储数据库表结构相关信息
  ddl_script              String?
  init_script             String?
  pipeline                Json?
  deprecated              Boolean               @default(false)
  dataset_id              String                @db.Uuid
  // data_source_id          String                @db.Uuid
  dataset                 Dataset               @relation(fields: [dataset_id], references: [id])
  // data_source             DataSource            @relation(fields: [data_source_id], references: [id])
  source_relationships    EntityRelationship[]  @relation("source")
  target_relationships    EntityRelationship[]  @relation("target")
  workspace_data_entities WorkspaceDataEntity[]
  components              EntityComponent[]

  @@unique([dataset_id, name])
  @@map("data_entities")
}

model EntityRelationship {
  id               String       @id @db.Uuid
  created_at       DateTime     @default(now()) @db.Timestamptz()
  updated_at       DateTime     @updatedAt @db.Timestamptz()
  related_fields   String[]
  relation_type    RelationType
  source_entity_id String       @db.Uuid
  target_entity_id String       @db.Uuid
  source_entity    DataEntity   @relation("source", fields: [source_entity_id], references: [id])
  target_entity    DataEntity   @relation("target", fields: [target_entity_id], references: [id])

  @@map("entity_relationships")
}

model Workspace {
  id                      String                @id @db.Uuid
  name                    String                @db.VarChar(100)
  description             String?
  logo                    Bytes?                @db.ByteA
  created_at              DateTime              @default(now()) @db.Timestamptz()
  updated_at              DateTime              @updatedAt @db.Timestamptz()
  apps                    App[]
  components              EntityComponent[]
  workspace_data_entities WorkspaceDataEntity[]
  Dataset                 Dataset[]
  WorkflowJob             WorkflowJob[]

  @@map("workspaces")
}

model WorkspaceDataEntity {
  workspace_id   String     @db.Uuid
  data_entity_id String     @db.Uuid
  created_at     DateTime   @default(now()) @db.Timestamptz()
  data_entity    DataEntity @relation(fields: [data_entity_id], references: [id])
  workspace      Workspace  @relation(fields: [workspace_id], references: [id])
  // file_path             String     @db.VarChar(500)
  // qualified_entity_name String     @db.VarChar(100)

  @@id([workspace_id, data_entity_id])
  @@map("workspace_data_entities")
}

model EntityComponent {
  id             String        @id @db.Uuid
  name           String        @db.VarChar(100)
  created_at     DateTime      @default(now())
  updated_at     DateTime      @updatedAt
  component_type ComponentType
  data_entity_id String        @db.Uuid
  data_entity    DataEntity    @relation(fields: [data_entity_id], references: [id])
  config         Json
  workspace_id   String        @db.Uuid
  workspace      Workspace     @relation(fields: [workspace_id], references: [id])

  @@map("entity_components")
}

model App {
  id           String      @id @db.Uuid
  name         String      @db.VarChar(100)
  created_at   DateTime    @default(now()) @db.Timestamptz()
  updated_at   DateTime    @updatedAt @db.Timestamptz()
  config       Json
  workspace_id String      @db.Uuid
  entry        Json
  menu_items   Json
  workspace    Workspace   @relation(fields: [workspace_id], references: [id])
  app_domains  AppDomain[]

  @@map("apps")
}

model AppDomain {
  id          String        @id @db.Uuid
  name        String        @db.VarChar(100)
  created_at  DateTime      @default(now()) @db.Timestamptz()
  updated_at  DateTime      @updatedAt @db.Timestamptz()
  config      Json
  app_id      String        @db.Uuid
  tenant_id   String        @db.Uuid
  app         App           @relation(fields: [app_id], references: [id])
  tenant      Tenant        @relation(fields: [tenant_id], references: [id])
  WorkflowJob WorkflowJob[]

  @@map("app_domains")
}

model ExternalApi {
  id          String        @id @db.Uuid
  name        String        @db.VarChar(100)
  description String?
  base_url    String        @db.VarChar(1024)
  auth_config Json?
  components  Json?
  created_at  DateTime      @default(now()) @db.Timestamptz()
  updated_at  DateTime      @updatedAt @db.Timestamptz()
  endpoints   ApiEndpoint[]

  @@map("external_apis")
}

model ApiEndpoint {
  id              String      @id @db.Uuid
  name            String      @db.VarChar(100)
  description     String?
  external_api    ExternalApi @relation(fields: [external_api_id], references: [id])
  external_api_id String      @db.Uuid
  method          HttpMethod
  path            String      @db.VarChar(1024)
  parameters      Json?
  request_body    Json?
  response        Json
  ingest_configs  Json?
  created_at      DateTime    @default(now()) @db.Timestamptz()
  updated_at      DateTime    @updatedAt @db.Timestamptz()

  @@map("api_endpoints")
}

model Workflow {
  id           String        @id @db.Uuid
  workspace_id String        @db.Uuid
  name         String        @db.VarChar(100)
  version      Int           @default(1)
  description  String?
  dag          Json          @default("{}") // React Flow DAG JSON
  created_at   DateTime      @default(now()) @db.Timestamptz()
  updated_at   DateTime      @updatedAt @db.Timestamptz()
  jobs         WorkflowJob[]

  @@map("workflows")
}

model WorkflowJob {
  id               String        @id @db.Uuid
  parent_job_id    String?       @db.Uuid
  parent_job       WorkflowJob?  @relation("JobParent", fields: [parent_job_id], references: [id])
  child_jobs       WorkflowJob[] @relation("JobParent")
  workflow_id      String        @db.Uuid
  workflow         Workflow      @relation(fields: [workflow_id], references: [id])
  workflow_node_id String?       @db.VarChar(50)
  workspace_id     String?       @db.Uuid
  workspace        Workspace?    @relation(fields: [workspace_id], references: [id])
  app_domain_id    String?       @db.Uuid
  app_domain       AppDomain?    @relation(fields: [app_domain_id], references: [id])
  execution_id     String        @db.Uuid // 标识一次整体 workflow 执行（例如 workflow_jobs 里同一个 DAG 实例的所有节点共享） @map("execution_id")
  parameters       Json?
  result           Json?
  status           JobStatus
  error_message    String?

  start_time DateTime  @db.Timestamptz()
  end_time   DateTime? @db.Timestamptz()

  @@index([workflow_id])
  @@index([execution_id])
  @@index([parent_job_id])
  @@map("workflow_jobs")
}

enum JobStatus {
  Pending
  Running
  Success
  Failed
  Canceled
}

enum HttpMethod {
  Get
  Post
}

enum ResouceType {
  Dataset
  Workspace
  Tenant
  AppDomain
}

enum EntityType {
  Table
  View
}

enum DataSourceType {
  FetchFusion
  Api
  Ftp
  File
  Database
  S3
  View
}

enum RelationType {
  ONE_TO_ONE
  ONE_TO_MANY
}

enum ComponentType {
  Chart
  Table
  Card
  Detail
}

enum RoleType {
  SysAdmin
  DataManager
  PublishManager
  Editor
  AppUser
}
