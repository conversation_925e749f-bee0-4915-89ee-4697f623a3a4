import { initTRPC } from "@trpc/server";
import { z } from "zod";

const t = initTRPC.create();
const publicProcedure = t.procedure;

const appRouter = t.router({
  dataSource: t.router({
    listTables: publicProcedure.input(z.object({
      workspaceId: z.string().uuid().nullable(),
    })).output(z.record(z.string(), z.object({
      name: z.string(),
      description: z.string().nullable(),
      data_entities: z
        .object({
          id: z.string(),
          name: z.string(),
          description: z.string().nullable(),
        })
        .array(),
    }).array())).query(async () => "PLACEHOLDER_DO_NOT_REMOVE" as any),
    listViews: publicProcedure.input(z.object({
      workspaceId: z.string().uuid().nullable(),
    })).output(z.array(z.object({
      name: z.string(),
      description: z.string().nullable(),
      data_entities: z
        .object({
          id: z.string(),
          name: z.string(),
          description: z.string().nullable(),
        })
        .array(),
    }))).query(async () => "PLACEHOLDER_DO_NOT_REMOVE" as any),
    getEntityStructure: publicProcedure.input(z.object({ entityId: z.string().uuid() })).query(async () => "PLACEHOLDER_DO_NOT_REMOVE" as any),
    querySampleData: publicProcedure.input(z.object({ entityId: z.string().uuid(), page: z.number().default(1), pageSize: z.number().default(10) })).output(z.object({ totalCount: z.number(), records: z.any().array() })).query(async () => "PLACEHOLDER_DO_NOT_REMOVE" as any)
  }),
  upload: t.router({ getPresignedUrl: publicProcedure.input(z.object({ fileName: z.string(), fileType: z.string() })).mutation(async () => "PLACEHOLDER_DO_NOT_REMOVE" as any) })
});
export type AppRouter = typeof appRouter;

