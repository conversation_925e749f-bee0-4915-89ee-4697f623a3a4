// import { DatabaseService } from "./database.service.ts";
import { Router, Query, Input } from 'nestjs-trpc';
import { z } from 'zod/v3';
import { AppConfigService } from '@server/config/config.service';
import { PrismaService } from '@server/common/prisma.service';
import { PublicWorkspaceId } from '@server/common/constants';
import { DataSourceType, EntityType } from '@prisma/client';
import { DuckDBInstance } from '@duckdb/node-api';

// const dataSourceSchema = z.object({});
const WorkspaceInputSchema = z.object({
  workspaceId: z.string().uuid().nullable(),
});

const DatasetSchema = z.object({
  name: z.string(),
  description: z.string().nullable(),
  data_entities: z
    .object({
      id: z.string(),
      name: z.string(),
      description: z.string().nullable(),
    })
    .array(),
});

const DataSourceCatelogSchema = z.record(z.string(), DatasetSchema.array());

type DataSourceCatelog = z.infer<typeof DataSourceCatelogSchema>;

const OutputSchema = z.object({ totalCount: z.number(), records: z.any().array() });

@Router({ alias: 'dataSource' })
export class DataSourceRouter {
  constructor(
    private readonly configService: AppConfigService,
    private readonly prisma: PrismaService,
  ) {}

  @Query({ input: WorkspaceInputSchema, output: DataSourceCatelogSchema })
  async listTables(@Input('workspaceId') workspaceId?: string): Promise<DataSourceCatelog> {
    const data = await this.prisma.dataset.findMany({
      where: {
        workspace_id: workspaceId || PublicWorkspaceId,
        deprecated: false,
      },
      select: {
        name: true,
        description: true,
        source_type: true,
        data_entities: {
          select: {
            id: true,
            name: true,
            description: true,
          },
          where: {
            deprecated: false,
            entity_type: EntityType.Table,
          },
        },
      },
    });
    const catelog: DataSourceCatelog = {};
    for (const dataset of data) {
      let catelogKey: string;
      switch (dataset.source_type) {
        case DataSourceType.Api:
        case DataSourceType.FetchFusion:
          catelogKey = '采集器';
          break;
        case DataSourceType.Database:
          catelogKey = '数据库';
          break;
        case DataSourceType.Ftp:
        case DataSourceType.S3:
          catelogKey = '远程文件';
          break;
        default:
          catelogKey = '本地文件';
      }
      if (catelog[catelogKey]) {
        catelog[catelogKey].push(dataset);
      } else {
        catelog[catelogKey] = [dataset];
      }
    }
    return catelog;
  }

  @Query({ input: WorkspaceInputSchema, output: z.array(DatasetSchema) })
  async listViews(@Input('workspaceId') workspaceId?: string): Promise<z.infer<typeof DatasetSchema>[]> {
    const data = await this.prisma.dataset.findMany({
      where: {
        workspace_id: workspaceId || PublicWorkspaceId,
        deprecated: false,
        source_type: DataSourceType.View,
      },
      select: {
        name: true,
        description: true,
        data_entities: {
          select: {
            id: true,
            name: true,
            description: true,
          },
          where: {
            deprecated: false,
            entity_type: EntityType.View,
          },
        },
      },
    });
    return data;
  }

  @Query({ input: z.object({ entityId: z.string().uuid() }) })
  async getEntityStructure(@Input('entityId') entityId: string) {
    const data = await this.prisma.dataEntity.findUniqueOrThrow({
      where: {
        id: entityId,
      },
      select: {
        structure: true,
      },
    });
    return data.structure;
  }

  @Query({
    input: z.object({ entityId: z.string().uuid(), page: z.number().default(1), pageSize: z.number().default(10) }),
    output: OutputSchema,
  })
  async querySampleData(
    @Input('entityId') entityId: string,
    @Input('page') page: number,
    @Input('pageSize') pageSize: number,
  ): Promise<z.infer<typeof OutputSchema>> {
    const entity = await this.prisma.dataEntity.findUniqueOrThrow({
      where: {
        id: entityId,
      },
      select: {
        dataset: {
          select: {
            workspace_id: true,
            namespace: true,
          },
        },
        name: true,
      },
    });
    const dataFilePath = this.configService.getSampleDataFilePath(
      entity.dataset.workspace_id,
      entity.dataset.namespace,
    );
    const instance = await DuckDBInstance.fromCache(dataFilePath);
    const connection = await instance.connect();
    try {
      const countResult = await connection.runAndRead(`select count(1) from ${entity.name}`);
      await countResult.readAll();
      const count = Number(countResult.value(0, 0));
      if (count === 0) {
        return {
          totalCount: 0,
          records: [],
        };
      }
      const offset = (page - 1) * pageSize;
      const dataResult = await connection.runAndRead(`select * from ${entity.name} limit ${pageSize} offset ${offset}`);
      await dataResult.readAll();
      const records = dataResult.getRowObjectsJson();
      const result = {
        totalCount: count,
        records,
      };
      return result;
    } finally {
      connection.closeSync();
    }
  }
}
