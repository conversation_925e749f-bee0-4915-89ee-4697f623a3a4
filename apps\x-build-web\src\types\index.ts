import type { TablePaginationConfig, TreeDataNode } from "antd";

export interface CustomTreeNode extends TreeDataNode {
  type: string;
  editable?: boolean;
  tableType?: "db" | "excel";
  usedInWorkspace?: boolean;
  children?: CustomTreeNode[];
  entityId?: string; // 数据实体ID，用于后续查询
  datasetName?: string; // 数据集名称
  description?: string | null; // 描述信息
}
export interface DatabaseInfo {
  name: string;
  type: string;
  host: string;
  port: number;
  username: string;
  password: string;
}
export interface StructureItem {
  key: string;
  id: number;
  type: string;
  name: string;
  description: string;
}
export interface DataItem {
  key: string;
  majorName: string;
  majorDirection: string;
  analyst: string;
  time: string;
  platform: string;
  voiceVolume: number;
  interactionRate: number;
  commentCount: number;
  forwardCount: number;
  likeCount: number;
  viewCount: number;
}

export interface EChartProps {
  options: any
  onClick?: any
  onContextMenu?: any
  onMouseMove?: any
  onRoam?: any
  setDropDownOpen?: any
}
export interface PieChartProps {
  data: any[]
  tags: any[]
  metrics: any[]
  tagDims: any[]
  name?: string
  chartId?: number
  drillDown?: (id: number, path: string, tagDims: any[]) => void
  path?: any[]
  setPath?: React.Dispatch<React.SetStateAction<any[]>>
}

export interface ScatterChartProps {
  data: any[]
  tags: any[]
  tagDims: any[]
  metrics: any[]
  name?: string
  chartId?: number
  period?: any[]
  drillDown?: (id: number, path: string, tagDims: any[]) => void
  path?: any[]
  setPath?: React.Dispatch<React.SetStateAction<any[]>>
}

export interface RadarChartProps {
  data: any[]
  tags: any[]
  tagDims: any[]
  metrics: any[]
  views: any[]
  name?: string
  chartId?: number
  drillDown?: (id: number, path: string, tagDims: any[]) => void
  path?: any[]
  setPath?: React.Dispatch<React.SetStateAction<any[]>>
}

export interface BarChartProps {
  data: any[]
  views: any[]
  tags: any[]
  period?: any[]
  metrics: any[]
  tagDims: any[]
  name?: string
  path?: any[]
  setPath?: React.Dispatch<React.SetStateAction<any[]>>
  chartId?: number
  drillDown?: (id: number, path: string, tagDims: any[]) => void
}

export interface HistoChartProps {
  data: any[]
  tags: any[]
  metrics: any[]
  views: any[]
  period?: any[]
  tagDims: any[]
  name?: string
  path?: any[]
  paths?: any[]
  setPath?: React.Dispatch<React.SetStateAction<any[]>>
  chartId?: number
  drillDown?: (id: number, path: string, tagDims: any[]) => void
}

export interface ComboChartProps {
  data: any[]
  views: any[]
  tags: any[]
  metrics: any[]
  tagDims: any[]
  name?: string
  path?: any[]
  paths?: any[]
  setPath?: React.Dispatch<React.SetStateAction<any[]>>
  chartId?: number
  drillDown?: (id: number, path: string, tagDims: any[]) => void
}

export interface DoughnutChartProps {
  data: any[]
  roseType: any
  metrics: any[]
  tags: any[]
  tagDims: any[]
  name?: string
  path?: any[]
  setPath?: React.Dispatch<React.SetStateAction<any[]>>
  chartId?: number
  paths?: any[]
  drillDown?: (id: number, path: string, tagDims: any[]) => void
}

export interface TreemapChartProps {
  data: any[]
  metrics: any[]
  tags: any[]
  tagDims: any[]
  name?: string
  path?: any[]
  setPath?: React.Dispatch<React.SetStateAction<any[]>>
  chartId?: number
  paths?: any[]
  drillDown?: (id: number, path: string, tagDims: any[]) => void
}

export interface FunnelChartProps {
  data: any[]
  metrics: any[]
  tags: any[]
  tagDims: any[]
  name?: string
  path?: any[]
  setPath?: React.Dispatch<React.SetStateAction<any[]>>
  chartId?: number
  drillDown?: (id: number, path: string, tagDims: any[]) => void
  paths?: any[]
}

export interface SankeyChartProps {
  data: any[]
  name?: string
}

export interface GraphChartProps {
  data: any[]
  tags: any[]
  name?: string
  metrics: any[]
  tagDims: any[]
  chartId?: number
  paths?: any[]
  graphData: { title: string } | undefined
  drillChartGraph?: (params: any, setOptions: any) => void
  drillDashBoardGraph?: (
    id: number,
    params: any,
    tagDims: any[],
    metrics: any,
    setOptions: any
  ) => void
  crossGraph?: (
    id: number,
    cross: any,
    dims: any[],
    metrics: any[],
    name: any,
    setOptions: any
  ) => void
}

export interface MapChartProps {
  views: any[]
  tagDims: any[]
  data: any[]
  metrics: any[]
  tags: any[]
  name?: string
  map?: string
  path?: any[]
  paths?: any[]
  setPath?: React.Dispatch<React.SetStateAction<any[]>>
  chartId?: number
  drillDown?: (id: number, path: string, tagDims: any, type: any) => void
  drillUp?: (id: number, index: number, tagDims: any, type: any) => void
}

export interface LineChartProps {
  metrics: any[]
  data: any[]
  areaStyle?: any
  period: any
  views: any[]
  tags: any[]
  tagDims: any[]
  name?: string
  path?: any[]
  paths?: any[]
  setPath?: React.Dispatch<React.SetStateAction<any[]>>
  chartId?: number
  drillDown?: (id: number, path: string, tagDims: any[]) => void
}

export interface WordCloudChartProps {
  metrics: any[]
  data: any[]
  tags: any[]
  tagDims: any[]
  views: any[]
  name?: string
}
export interface BarStackChartProps {
  name?: string
  views: any[]
  chartId?: number
  data: any[]
  metrics: any[]
  currentCategory: any[]
  tags: any[]
  tagDims: any[]
  crosses: any[]
  setCrosses: React.Dispatch<React.SetStateAction<any[]>>
}
export type MetricsType =
  | 'total'
  | 'match'
  | 'tgi'
  | 'rebase'
  | 'increase_rate'
  | 'tag'
export interface MetricCardProps {
  data: any[]
  metrics: string[]
  cardData: any
  setCardData?: React.Dispatch<React.SetStateAction<any>>
  setMetrics?: React.Dispatch<React.SetStateAction<any>>
  tags: any[]
  name?: string
  bizType: number
  period?: any[]
}
export interface ChartTableProps {
  data: any[]
  metrics: any[]
  views: any[]
  tags: any[]
  period?: any[]
  name?: string
}

export interface EChartProps {
  options: any
  onClick?: any
  onContextMenu?: any
  onMouseMove?: any
  onRoam?: any
  setDropDownOpen?: any
}

interface BaseTgiTable {
  header: {
    tag_level: number
    tag: string
    parent_tag?: string
  }
  total: {
    count: number
    rate: number
  }
  [key: string]: any
}

interface TgiTableMonth extends BaseTgiTable {
  month: string
}

interface TgiTableQuarter extends BaseTgiTable {
  quarter: string
}

interface TgiTableHalfyear extends BaseTgiTable {
  halfyear: string
}

interface TgiTableYear extends BaseTgiTable {
  year: string
}

export interface TableParams {
  pagination: TablePaginationConfig
  searchName?: string
}

export interface ViewTableParams {
  pagination: TablePaginationConfig
  searchViewName?: string
}
export interface TgiKey {
  [key: string]: any
}
export type TgiTableData =
  | BaseTgiTable
  | TgiTableMonth
  | TgiTableQuarter
  | TgiTableHalfyear
  | TgiTableYear
  | TgiKey