import { Injectable } from '@nestjs/common';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { AppConfigService } from '@server/config/config.service';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

@Injectable()
export class S3Service {
  private readonly s3Client: S3Client;

  constructor(private readonly configService: AppConfigService) {
    this.s3Client = new S3Client(this.configService.S3Config);
  }

  async uploadFile(key: string, body: Buffer | string) {
    const command = new PutObjectCommand({
      Bucket: this.configService.S3DefaultBucket,
      Key: key,
      Body: body,
    });
    return this.s3Client.send(command);
  }

  async getPresignedUrl(key: string, fileType: string) {
    const command = new PutObjectCommand({
      Bucket: this.configService.S3DefaultBucket,
      Key: key,
      ContentType: fileType,
    });
    const url = await getSignedUrl(this.s3Client, command, { expiresIn: 300 });
    return { url, key };
  }

  getClient() {
    return this.s3Client;
  }
}
